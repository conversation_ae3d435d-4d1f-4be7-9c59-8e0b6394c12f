"""
RAG Agent for answering questions using retrieved context.
"""
import logging
from typing import List, Dict, Any, Optional
from mistralai import Mistra<PERSON>
from sentence_transformers import SentenceTransformer

from config import API_CONFIG, PROCESSING_CONFIG, RAG_SYSTEM_PROMPT, QUESTION_TYPES
from services.vector_store import VectorStore

logger = logging.getLogger(__name__)

class RAGAgent:
    """RAG agent for question answering using context retrieval."""
    
    def __init__(self, vector_store: VectorStore):
        """
        Initialize RAG agent.
        
        Args:
            vector_store: Configured vector store instance
        """
        self.vector_store = vector_store
        
        # Initialize Mistral client
        self.mistral_client = Mistral(api_key=API_CONFIG.mistral_api_key)
        self.model = API_CONFIG.mistral_model
        
        # Initialize embedding model for query encoding
        self.embedding_model = SentenceTransformer(PROCESSING_CONFIG.embedding_model)
        
    def answer_question(self, question_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Answer a single question using RAG.
        
        Args:
            question_data: Question dictionary with text, type, options, etc.
            
        Returns:
            Question data with answer added
        """
        try:
            question_text = question_data["question_text"]
            question_type = question_data["question_type"]
            
            logger.info(f"Answering question: {question_text[:100]}...")
            
            # Retrieve relevant context
            context = self._retrieve_context(question_text)
            
            # Generate answer based on question type
            answer = self._generate_answer(question_text, question_type, context, question_data.get("options"))
            
            # Update question data with answer
            question_data["answer"] = answer
            question_data["context_used"] = len(context)
            
            return question_data
            
        except Exception as e:
            logger.error(f"Error answering question: {str(e)}")
            question_data["answer"] = "Error: Could not generate answer"
            question_data["error"] = str(e)
            return question_data
    
    def _retrieve_context(self, question: str, top_k: int = None) -> List[str]:
        """
        Retrieve relevant context for a question.
        
        Args:
            question: Question text
            top_k: Number of context chunks to retrieve
            
        Returns:
            List of relevant text chunks
        """
        try:
            # Generate query embedding
            query_embedding = self.embedding_model.encode(
                question, 
                prompt_name="query"
            ).tolist()
            
            # Search for similar chunks
            similar_chunks = self.vector_store.search_similar(
                query_embedding=query_embedding,
                top_k=top_k or PROCESSING_CONFIG.top_k_results
            )
            
            # Extract text from chunks
            context_texts = [chunk["text"] for chunk in similar_chunks]
            
            logger.info(f"Retrieved {len(context_texts)} context chunks for question")
            return context_texts
            
        except Exception as e:
            logger.error(f"Error retrieving context: {str(e)}")
            return []
    
    def _generate_answer(
        self, 
        question: str, 
        question_type: str, 
        context: List[str], 
        options: Optional[List[str]] = None
    ) -> str:
        """
        Generate answer using Mistral LLM.
        
        Args:
            question: Question text
            question_type: Type of question
            context: Retrieved context chunks
            options: Question options (if applicable)
            
        Returns:
            Generated answer
        """
        try:
            # Prepare context string
            context_str = "\n\n".join(context) if context else "No relevant context found."
            
            # Create question-specific prompt
            user_prompt = self._create_question_prompt(question, question_type, context_str, options)
            
            # Generate response
            response = self.mistral_client.chat.complete(
                model=self.model,
                messages=[
                    {"role": "system", "content": RAG_SYSTEM_PROMPT},
                    {"role": "user", "content": user_prompt}
                ]
            )
            
            answer = response.choices[0].message.content.strip()
            logger.info(f"Generated answer for {question_type} question")
            
            return answer
            
        except Exception as e:
            logger.error(f"Error generating answer: {str(e)}")
            return f"Error generating answer: {str(e)}"
    
    def _create_question_prompt(
        self, 
        question: str, 
        question_type: str, 
        context: str, 
        options: Optional[List[str]] = None
    ) -> str:
        """
        Create a question-specific prompt for the LLM.
        
        Args:
            question: Question text
            question_type: Type of question
            context: Retrieved context
            options: Question options (if applicable)
            
        Returns:
            Formatted prompt string
        """
        base_prompt = f"""
Context:
{context}

Question: {question}
"""
        
        if question_type == QUESTION_TYPES.MULTIPLE_CHOICE_SINGLE:
            if options:
                options_str = "\n".join([f"{chr(65+i)}. {opt}" for i, opt in enumerate(options)])
                base_prompt += f"""
Options:
{options_str}

Please select the single correct answer and explain your reasoning. Format: "Answer: X (explanation)"
"""
            else:
                base_prompt += "\nPlease provide the correct answer and explain your reasoning."
                
        elif question_type == QUESTION_TYPES.MULTIPLE_CHOICE_MULTI:
            if options:
                options_str = "\n".join([f"{chr(65+i)}. {opt}" for i, opt in enumerate(options)])
                base_prompt += f"""
Options:
{options_str}

Please select ALL correct answers and explain your reasoning. Format: "Answers: X, Y, Z (explanation)"
"""
            else:
                base_prompt += "\nPlease provide all correct answers and explain your reasoning."
                
        elif question_type == QUESTION_TYPES.TRUE_FALSE:
            base_prompt += "\nPlease answer True or False and provide a brief explanation. Format: \"Answer: True/False (explanation)\""
            
        elif question_type == QUESTION_TYPES.FILL_IN_BLANK:
            base_prompt += "\nPlease provide the word(s) or phrase(s) that should fill in the blank(s). Be precise and concise."
            
        elif question_type == QUESTION_TYPES.MATCH_FOLLOWING:
            base_prompt += "\nPlease provide the correct matches and explain your reasoning."
            
        else:  # TEXTUAL_ANSWER
            base_prompt += "\nPlease provide a comprehensive answer based on the context provided."
        
        return base_prompt

def answer_all_questions(questions_json: Dict[str, Any], vector_store: VectorStore) -> Dict[str, Any]:
    """
    Answer all questions in the JSON using RAG.
    
    Args:
        questions_json: JSON containing all extracted questions
        vector_store: Configured vector store
        
    Returns:
        Updated JSON with answers
    """
    rag_agent = RAGAgent(vector_store)
    
    total_questions = len(questions_json["questions"])
    logger.info(f"Starting to answer {total_questions} questions")
    
    for i, question_data in enumerate(questions_json["questions"]):
        logger.info(f"Processing question {i+1}/{total_questions}")
        answered_question = rag_agent.answer_question(question_data)
        questions_json["questions"][i] = answered_question
    
    # Update metadata
    questions_json["answered_questions"] = total_questions
    questions_json["rag_processing_complete"] = True
    
    logger.info(f"Completed answering all {total_questions} questions")
    return questions_json
