"""
Knowledge base processing and chunking service.
"""
import logging
from typing import List, Dict, Any
from pathlib import Path
import re
from sentence_transformers import SentenceTransformer

from config import PROCESSING_CONFIG

logger = logging.getLogger(__name__)

class KnowledgeProcessor:
    """Processes and chunks knowledge base documents."""
    
    def __init__(self, embedding_model: str = None):
        """
        Initialize knowledge processor.
        
        Args:
            embedding_model: Name of the embedding model to use
        """
        self.embedding_model_name = embedding_model or PROCESSING_CONFIG.embedding_model
        self.chunk_size = PROCESSING_CONFIG.chunk_size
        self.chunk_overlap = PROCESSING_CONFIG.chunk_overlap
        
        # Load embedding model
        logger.info(f"Loading embedding model: {self.embedding_model_name}")
        self.embedding_model = SentenceTransformer(self.embedding_model_name)
        
    def load_knowledge_base(self, file_path: str) -> str:
        """
        Load knowledge base from file.
        
        Args:
            file_path: Path to the knowledge base file
            
        Returns:
            Raw text content
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            logger.info(f"Loaded knowledge base from {file_path}, {len(content)} characters")
            return content
            
        except Exception as e:
            logger.error(f"Error loading knowledge base from {file_path}: {str(e)}")
            raise
    
    def chunk_text_recursive(self, text: str) -> List[Dict[str, Any]]:
        """
        Chunk text using recursive strategy with overlap.
        
        Args:
            text: Input text to chunk
            
        Returns:
            List of chunk dictionaries
        """
        # Split by major sections first (markdown headers)
        sections = self._split_by_headers(text)
        
        chunks = []
        chunk_id = 0
        
        for section_title, section_content in sections:
            # Further split large sections
            section_chunks = self._split_section(section_content, section_title)
            
            for chunk_text in section_chunks:
                if len(chunk_text.strip()) > 50:  # Minimum chunk size
                    chunk = {
                        "chunk_id": chunk_id,
                        "text": chunk_text.strip(),
                        "section": section_title,
                        "char_count": len(chunk_text),
                        "metadata": {
                            "section_title": section_title,
                            "chunk_index": len(chunks)
                        }
                    }
                    chunks.append(chunk)
                    chunk_id += 1
        
        logger.info(f"Created {len(chunks)} chunks from knowledge base")
        return chunks
    
    def _split_by_headers(self, text: str) -> List[tuple]:
        """Split text by markdown headers."""
        # Pattern to match markdown headers
        header_pattern = r'^(#{1,6})\s+(.+)$'
        
        sections = []
        current_section = ""
        current_title = "Introduction"
        
        lines = text.split('\n')
        
        for line in lines:
            header_match = re.match(header_pattern, line, re.MULTILINE)
            
            if header_match:
                # Save previous section
                if current_section.strip():
                    sections.append((current_title, current_section.strip()))
                
                # Start new section
                current_title = header_match.group(2).strip()
                current_section = line + '\n'
            else:
                current_section += line + '\n'
        
        # Add final section
        if current_section.strip():
            sections.append((current_title, current_section.strip()))
        
        return sections
    
    def _split_section(self, text: str, section_title: str) -> List[str]:
        """Split a section into smaller chunks."""
        if len(text) <= self.chunk_size:
            return [text]
        
        # Split by paragraphs first
        paragraphs = text.split('\n\n')
        
        chunks = []
        current_chunk = ""
        
        for paragraph in paragraphs:
            # If adding this paragraph would exceed chunk size
            if len(current_chunk) + len(paragraph) > self.chunk_size and current_chunk:
                # Add overlap from previous chunk
                if chunks:
                    overlap_text = self._get_overlap_text(chunks[-1])
                    current_chunk = overlap_text + current_chunk
                
                chunks.append(current_chunk.strip())
                current_chunk = paragraph
            else:
                if current_chunk:
                    current_chunk += '\n\n' + paragraph
                else:
                    current_chunk = paragraph
        
        # Add final chunk
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def _get_overlap_text(self, previous_chunk: str) -> str:
        """Get overlap text from previous chunk."""
        if len(previous_chunk) <= self.chunk_overlap:
            return previous_chunk + '\n\n'
        
        # Take last chunk_overlap characters, but try to break at sentence boundary
        overlap_text = previous_chunk[-self.chunk_overlap:]
        
        # Find last sentence boundary
        sentence_end = max(
            overlap_text.rfind('.'),
            overlap_text.rfind('!'),
            overlap_text.rfind('?')
        )
        
        if sentence_end > self.chunk_overlap // 2:
            overlap_text = overlap_text[sentence_end + 1:]
        
        return overlap_text.strip() + '\n\n'
    
    def generate_embeddings(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Generate embeddings for text chunks.
        
        Args:
            chunks: List of chunk dictionaries
            
        Returns:
            Chunks with embeddings added
        """
        logger.info(f"Generating embeddings for {len(chunks)} chunks")
        
        # Extract texts for batch processing
        texts = [chunk["text"] for chunk in chunks]
        
        # Generate embeddings in batches
        embeddings = self.embedding_model.encode(
            texts,
            batch_size=32,
            show_progress_bar=True,
            convert_to_numpy=True
        )
        
        # Add embeddings to chunks
        for i, chunk in enumerate(chunks):
            chunk["embedding"] = embeddings[i].tolist()
            chunk["embedding_model"] = self.embedding_model_name
        
        logger.info("Embeddings generated successfully")
        return chunks

def process_knowledge_base(kb_file_path: str) -> List[Dict[str, Any]]:
    """
    Process knowledge base file and return chunks with embeddings.
    
    Args:
        kb_file_path: Path to knowledge base file
        
    Returns:
        List of processed chunks with embeddings
    """
    processor = KnowledgeProcessor()
    
    # Load knowledge base
    text = processor.load_knowledge_base(kb_file_path)
    
    # Chunk the text
    chunks = processor.chunk_text_recursive(text)
    
    # Generate embeddings
    chunks_with_embeddings = processor.generate_embeddings(chunks)
    
    return chunks_with_embeddings
