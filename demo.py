"""
Demo script to showcase the PDF Question Extraction and RAG system.
"""
import json
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def demo_knowledge_processing():
    """Demo knowledge base processing."""
    print("🧪 Demo: Knowledge Base Processing")
    print("=" * 50)
    
    try:
        from services.knowledge_processor import process_knowledge_base
        
        kb_path = "files/IndianMovie_KnowledgeBase.md"
        if not Path(kb_path).exists():
            print(f"❌ Knowledge base not found: {kb_path}")
            return False
        
        print(f"📚 Processing knowledge base: {kb_path}")
        chunks = process_knowledge_base(kb_path)
        
        print(f"✅ Created {len(chunks)} chunks")
        print(f"📊 Sample chunk:")
        if chunks:
            sample = chunks[0]
            print(f"   Section: {sample['section']}")
            print(f"   Text: {sample['text'][:200]}...")
            print(f"   Embedding dimension: {len(sample['embedding'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_vector_store():
    """Demo vector store setup."""
    print("\n🧪 Demo: Vector Store Setup")
    print("=" * 50)
    
    try:
        from services.knowledge_processor import process_knowledge_base
        from services.vector_store import setup_vector_store
        
        kb_path = "files/IndianMovie_KnowledgeBase.md"
        if not Path(kb_path).exists():
            print(f"❌ Knowledge base not found: {kb_path}")
            return False
        
        print("📚 Processing knowledge base...")
        chunks = process_knowledge_base(kb_path)
        
        print("🗄️ Setting up vector store...")
        vector_store = setup_vector_store(chunks, force_recreate=True)
        
        # Test search
        print("🔍 Testing similarity search...")
        from sentence_transformers import SentenceTransformer
        from config import PROCESSING_CONFIG
        
        model = SentenceTransformer(PROCESSING_CONFIG.embedding_model)
        query = "Who directed Lagaan?"
        query_embedding = model.encode(query, prompt_name="query").tolist()
        
        results = vector_store.search_similar(query_embedding, top_k=3)
        
        print(f"✅ Found {len(results)} similar chunks for query: '{query}'")
        for i, result in enumerate(results):
            print(f"   {i+1}. Score: {result['score']:.3f}")
            print(f"      Text: {result['text'][:150]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_rag_answer():
    """Demo RAG answering."""
    print("\n🧪 Demo: RAG Question Answering")
    print("=" * 50)
    
    try:
        from services.knowledge_processor import process_knowledge_base
        from services.vector_store import setup_vector_store
        from services.rag_agent import RAGAgent
        
        kb_path = "files/IndianMovie_KnowledgeBase.md"
        if not Path(kb_path).exists():
            print(f"❌ Knowledge base not found: {kb_path}")
            return False
        
        print("📚 Setting up RAG system...")
        chunks = process_knowledge_base(kb_path)
        vector_store = setup_vector_store(chunks, force_recreate=True)
        
        rag_agent = RAGAgent(vector_store)
        
        # Test questions
        test_questions = [
            {
                "question_id": "demo_1",
                "question_text": "Who directed the movie Lagaan?",
                "question_type": "textual_answer",
                "options": None,
                "metadata": {"page_number": 1}
            },
            {
                "question_id": "demo_2", 
                "question_text": "Which film was the first to cross ₹100 crore at the box office?",
                "question_type": "textual_answer",
                "options": None,
                "metadata": {"page_number": 1}
            }
        ]
        
        print("🤖 Answering test questions...")
        for question in test_questions:
            print(f"\n❓ Question: {question['question_text']}")
            answered = rag_agent.answer_question(question)
            print(f"✅ Answer: {answered.get('answer', 'No answer')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_pdf_generation():
    """Demo PDF generation."""
    print("\n🧪 Demo: PDF Generation")
    print("=" * 50)
    
    try:
        from utils.pdf_generator import generate_answer_pdf
        
        # Sample questions JSON
        sample_json = {
            "total_questions": 2,
            "extraction_timestamp": 1640995200,  # 2022-01-01
            "rag_processing_complete": True,
            "questions": [
                {
                    "question_id": "demo_1",
                    "question_text": "Who directed the movie Lagaan?",
                    "question_type": "textual_answer",
                    "options": None,
                    "answer": "Ashutosh Gowariker directed Lagaan (2001).",
                    "context_used": 3,
                    "metadata": {"page_number": 1}
                },
                {
                    "question_id": "demo_2",
                    "question_text": "Which film was the first to cross ₹100 crore?",
                    "question_type": "textual_answer", 
                    "options": None,
                    "answer": "Ghajini (2008) was the first Indian film to cross ₹100 crore net domestic box office.",
                    "context_used": 2,
                    "metadata": {"page_number": 1}
                }
            ]
        }
        
        output_path = "demo_answers.pdf"
        print(f"📄 Generating PDF: {output_path}")
        
        success = generate_answer_pdf(sample_json, output_path)
        
        if success:
            print(f"✅ PDF generated successfully: {output_path}")
            return True
        else:
            print("❌ PDF generation failed")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Run all demos."""
    print("🚀 PDF Question Extraction and RAG System - Demo")
    print("=" * 60)
    
    demos = [
        ("Knowledge Processing", demo_knowledge_processing),
        ("Vector Store", demo_vector_store),
        ("RAG Answering", demo_rag_answer),
        ("PDF Generation", demo_pdf_generation)
    ]
    
    results = []
    
    for demo_name, demo_func in demos:
        try:
            result = demo_func()
            results.append((demo_name, result))
        except Exception as e:
            print(f"❌ {demo_name} demo failed: {e}")
            results.append((demo_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Demo Results Summary:")
    
    passed = 0
    for demo_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {demo_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} demos")
    
    if passed == len(results):
        print("\n🎉 All demos passed! The system is working correctly.")
        print("\nTo start the Streamlit app:")
        print("   streamlit run app.py")
    else:
        print(f"\n⚠️ {len(results) - passed} demo(s) failed.")

if __name__ == "__main__":
    main()
