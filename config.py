"""
Configuration settings for the PDF Question Extraction and RAG system.
"""
import os
from typing import Dict, Any
from dataclasses import dataclass

@dataclass
class APIConfig:
    """API configuration settings."""
    sambanova_api_key: str = "e828ddc3-8094-44dd-8f2b-b1624dfe2696"
    sambanova_base_url: str = "https://api.sambanova.ai/v1"
    sambanova_model: str = "Llama-4-Maverick-17B-128E-Instruct"
    
    mistral_api_key: str = "DNdGxx2Zgx7l0iTkkz44CzsYOOdEzUe7"
    mistral_model: str = "mistral-large-latest"
    
    qdrant_url: str = "https://a41fdff5-3272-490c-9b97-f2f70222a9fb.eu-west-1-0.aws.cloud.qdrant.io:6333"
    qdrant_api_key: str = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.vHNATr9Jl5-h4OOiQJElKZTKEw-_DrVLfjjbyrLaamg"

@dataclass
class ProcessingConfig:
    """Processing configuration settings."""
    # PDF processing
    pdf_dpi: int = 300
    image_format: str = "PNG"
    
    # VLM processing
    vlm_temperature: float = 0.1
    vlm_top_p: float = 0.1
    max_retries: int = 3
    
    # Embedding model
    embedding_model: str = "Snowflake/snowflake-arctic-embed-l-v2.0"
    embedding_dimension: int = 1024
    
    # Chunking
    chunk_size: int = 1000
    chunk_overlap: int = 200
    
    # Vector store
    collection_name: str = "knowledge_base"
    similarity_threshold: float = 0.7
    top_k_results: int = 5

@dataclass
class QuestionTypes:
    """Supported question types."""
    MULTIPLE_CHOICE_SINGLE: str = "multiple_choice_single"
    MULTIPLE_CHOICE_MULTI: str = "multiple_choice_multi"
    FILL_IN_BLANK: str = "fill_in_blank"
    TRUE_FALSE: str = "true_false"
    MATCH_FOLLOWING: str = "match_following"
    TEXTUAL_ANSWER: str = "textual_answer"

# Global configuration instances
API_CONFIG = APIConfig()
PROCESSING_CONFIG = ProcessingConfig()
QUESTION_TYPES = QuestionTypes()

# VLM Prompt Templates
VLM_SYSTEM_PROMPT = """
You are an expert OCR and question analysis system. Your task is to extract questions from images and identify their types.

Analyze the provided image and extract ALL questions visible in it. For each question, provide:
1. The complete question text
2. The question type
3. Any options (if applicable)
4. Any additional metadata

Question Types:
- multiple_choice_single: Single correct answer MCQ
- multiple_choice_multi: Multiple correct answers MCQ  
- fill_in_blank: Fill in the blank questions
- true_false: True/False questions
- match_following: Match the following questions
- textual_answer: Open-ended text questions

Return your response as a valid JSON array with this exact structure:
[
    {
        "question_id": "unique_id",
        "question_text": "extracted question text",
        "question_type": "one of the types above",
        "options": ["option1", "option2", ...] or null,
        "metadata": {
            "page_number": number,
            "confidence": float,
            "additional_info": "any relevant info"
        }
    }
]

Be extremely careful to extract questions accurately and completely. If no questions are found, return an empty array [].
"""

RAG_SYSTEM_PROMPT = """
You are an expert assistant specializing in Indian cinema, particularly Bollywood from 2000-2010. 
Use the provided context to answer questions accurately and comprehensively.

Guidelines:
1. Base your answers strictly on the provided context
2. If the context doesn't contain enough information, state this clearly
3. For multiple choice questions, select the most accurate option
4. For fill-in-the-blank, provide the exact word/phrase needed
5. For textual answers, be comprehensive but concise
6. Always maintain factual accuracy

Context will be provided before each question.
"""
